import React, { useState } from "react";
import CountText from "./CountText";
import GetLead from "./GetLead";
import { Button } from "@/components/ui/button";

type HeaderProps = React.FC<{
  isMyPurchases: boolean;
}>;
const Header: HeaderProps = ({ isMyPurchases }) => {
  const [leads, setLeads] = useState(0);
  return (
    <div className="flex flex-row items-center flex-wrap justify-between glassColor p-3 rounded-sm">
      {isMyPurchases ? (
        <>
          <CountText text="Total Purchased leads" count={20} />
        </>
      ) : (
        <>
          <CountText text="Filtered Results" count={4000} />
          <CountText text="New Leads" count={2584} />
          <GetLead leads={leads} setLeads={setLeads} />
          <Button className="bg-[#2563EB] px-2 py-1 text-white hover:bg-[#2563EB]/90">
            Get Lead (0 Credit)
          </Button>
        </>
      )}
    </div>
  );
};

export default Header;
