import React from "react";

type TabsProps = React.FC<{
  activeTab: boolean;
  setActiveTab: React.Dispatch<React.SetStateAction<boolean>>;
}>;
const Tabs: TabsProps = ({ activeTab, setActiveTab }) => {
  console.log("TabsProps activeTab", activeTab);
  return (
    <div className="w-full">
      <div className="w-full flex">
        <Tab
          text="New leads"
          isActive={activeTab === false}
          onClick={() => {
            setActiveTab(false);
          }}
        />
        <Tab
          text="My Purchased leads"
          onClick={() => {
            setActiveTab(true);
          }}
          isActive={activeTab}
        />
      </div>
      <div className="w-full bg-white h-1"></div>
    </div>
  );
};

export default Tabs;

type TabProps = React.FC<{
  text: string;
  isActive: boolean;
  onClick: () => void;
}>;
const Tab: TabProps = ({ onClick, isActive, text }) => {
  console.log({
    isActive,
    text,
  });
  return (
    <button
      onClick={onClick}
      className={`px-4 cursor-pointer py-3 rounded-t-sm shadow-none text-sm font-medium  ${
        isActive ? "bg-white" : "bg-transparent"
      }`}
    >
      {text}
    </button>
  );
};
