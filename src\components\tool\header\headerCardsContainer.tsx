import React from "react";

type HeaderCardsContainerProps = React.FC<{
  title: string;
  children: React.ReactNode;
}>;
const HeaderCardsContainer: HeaderCardsContainerProps = ({
  title,
  children,
}) => {
  return (
    <div className="flex flex-col w-full lg:w-1/2">
      <h1 className="text-[1rem] font-bold mb-1">{title}</h1>
      <div className="w-full">{children}</div>
    </div>
  );
};

export default HeaderCardsContainer;
