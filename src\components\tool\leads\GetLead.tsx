import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import React from "react";

const list = [
  {
    id: 1,
    value: 1,
  },
  {
    id: 2,
    value: 5,
  },
  {
    id: 3,
    value: 10,
  },
  {
    id: 4,
    value: 15,
  },
];

type GetLeadProps = React.FC<{
  leads: number;
  setLeads: React.Dispatch<React.SetStateAction<number>>;
}>;

const GetLead: GetLeadProps = ({ leads, setLeads }) => {
  return (
    <div className="flex flex-row items-center gap-2">
      <p className="m-0 text-[0.9rem] font-bold">Get Lead</p>
      <Select
        value={String(leads)}
        onValueChange={(val) => {
          setLeads(+val);
        }}
      >
        <SelectTrigger className="w-16 bg-white  text-[#14141FCC]">
          <SelectValue
            placeholder="Select number of leads"
            className="text-[#14141FCC]"
          />
        </SelectTrigger>
        <SelectContent>
          {list.map((item) => (
            <SelectItem key={item.value} value={String(item.value)}>
              {item.value}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default GetLead;
