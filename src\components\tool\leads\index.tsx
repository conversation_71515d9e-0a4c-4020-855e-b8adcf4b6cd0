"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { StarIcon } from "lucide-react";
import React, { useMemo, useState } from "react";
import Tabs from "./Tabs";
import Header from "./header";
import { Checkbox } from "@/components/ui/checkbox";

const mockLeads = [
  {
    id: "1",
    domain: "multiple-problems.com",
    contact: "Alyson",
    prospected: "Yes 10/10",
    teams: "Teams 1, Teams 4",
    score: 5,
    status: "Todo",
  },
  {
    id: "2",
    domain: "shop-family.com",
    contact: "<PERSON><PERSON><PERSON>",
    prospected: "Yes 10/10",
    teams: "Teams 1, Teams 4",
    score: 4,
    status: "Todo",
  },
  {
    id: "3",
    domain: "store.com",
    contact: "<PERSON><PERSON><PERSON>",
    prospected: "Yes 10/10",
    teams: "Teams 1, Teams 4",
    score: 4,
    status: "Todo",
  },
  {
    id: "4",
    domain: "tech-solutions.com",
    contact: "<PERSON>",
    prospected: "Yes 8/10",
    teams: "Teams 2, Teams 3",
    score: 3,
    status: "Todo",
  },
  {
    id: "5",
    domain: "business-hub.net",
    contact: "Sarah",
    prospected: "No 0/10",
    teams: "Teams 1",
    score: 5,
    status: "Todo",
  },
];

const Leads = () => {
  const [isMyPurchases, setIsMyPurchases] = useState(false);

  console.log("isMyPurchases", isMyPurchases);

  return (
    <div className="space-y-4">
      <Tabs activeTab={isMyPurchases} setActiveTab={setIsMyPurchases} />

      <Header isMyPurchases={isMyPurchases} />

      <Card className="glassColor border-white/30 overflow-hidden">
        <div className="overflow-x-auto bg-white">
          <Table>
            <TableHeader>
              <TableRow className="border-gray-200 bg-gray-50/50">
                <TableHead className="font-semibold text-gray-900 py-4">
                  Domain
                </TableHead>
                <TableHead className="font-semibold text-gray-900 py-4">
                  Contact
                </TableHead>
                <TableHead className="font-semibold text-gray-900 py-4">
                  Prospected
                </TableHead>
                <TableHead className="font-semibold text-gray-900 py-4">
                  Teams
                </TableHead>
                <TableHead className="font-semibold text-gray-900 py-4">
                  Score
                </TableHead>
                <TableHead className="font-semibold text-gray-900 py-4">
                  Status
                </TableHead>
                <TableHead className="w-40 py-4">
                  <Button className="primaryButton text-white px-4 py-2 text-xs rounded-lg font-medium">
                    Auto Enrich (0 Leads)
                  </Button>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockLeads.map((lead) => (
                <TableRow
                  key={lead.id}
                  className="border-gray-100 hover:bg-gray-50/30"
                >
                  <TableCell className="font-medium text-gray-900 py-4">
                    {lead.domain}
                  </TableCell>
                  <TableCell className="text-blue-600 py-4 font-medium">
                    {lead.contact}
                  </TableCell>
                  <TableCell className="text-gray-700 py-4">
                    {lead.prospected}
                  </TableCell>
                  <TableCell className="text-gray-700 py-4">
                    {lead.teams}
                  </TableCell>
                  <TableCell className="py-4">
                    <div className="flex items-center gap-[1px]">
                      <Outof total={lead.score} />
                      {renderStars(lead.score)}
                    </div>
                  </TableCell>
                  <TableCell className="py-4">
                    <Badge
                      variant="outline"
                      className="bg-yellow-100 text-yellow-800 border-yellow-300 font-medium"
                    >
                      {lead.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="py-4">
                    <Checkbox />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </Card>
    </div>
  );
};

export default Leads;

type OutofProps = React.FC<{
  total: number;
}>;
const Outof: OutofProps = ({ total }) => {
  const colors = useMemo(() => {
    if (+total >= 5) {
      return {
        bg: "#DCFCE7",
        color: "#16A34A",
      };
    }
    return {
      bg: "#FEF9C3",
      color: "#CA8A04",
    };
  }, [total]);

  return (
    <span
      style={{
        background: colors.bg,
        color: colors.color,
      }}
      className={`px-2 py-1 text-[0.75rem] rounded-full`}
    >
      {total}/6
    </span>
  );
};

const renderStars = (count: number) => {
  return Array.from({ length: 6 }, (_, i) => (
    <StarIcon
      key={i}
      className={`w-3 h-3 ${
        i < count ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
      }`}
    />
  ));
};
