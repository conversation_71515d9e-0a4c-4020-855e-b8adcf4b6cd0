import { createClient } from "@/lib/server";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();

    // Get the current user
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Get query parameters for pagination
    const { searchParams } = new URL(request.url);
    const skip = parseInt(searchParams.get("skip") || "0");
    const limit = parseInt(searchParams.get("limit") || "10");

    // Validate pagination parameters
    if (skip < 0 || limit < 1 || limit > 100) {
      return NextResponse.json(
        {
          error:
            "Invalid pagination parameters. Skip must be >= 0, limit must be between 1 and 100",
        },
        { status: 400 }
      );
    }

    // Fetch leads with pagination
    const { data: leads, error: leadsError } = await supabase
      .from("leads")
      .select("*")
      .order("updated_at", { ascending: false })
      .range(skip, skip + limit - 1);

    if (leadsError) {
      console.error("Failed to fetch leads:", leadsError);
      return NextResponse.json(
        { error: "Failed to fetch leads" },
        { status: 500 }
      );
    }

    // Get total count for pagination metadata
    const { count, error: countError } = await supabase
      .from("leads")
      .select("*", { count: "exact", head: true });

    if (countError) {
      console.error("Failed to fetch leads count:", countError);
      return NextResponse.json(
        { error: "Failed to fetch leads count" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: "Fetched leads successfully",
      success: true,
      data: {
        leads: leads || [],
        pagination: {
          skip,
          limit,
          total: count || 0,
          hasMore: skip + limit < (count || 0),
        },
      },
    });
  } catch (error) {
    console.error("Failed to fetch leads:", error);
    return NextResponse.json(
      { error: "Failed to fetch leads" },
      { status: 500 }
    );
  }
}
