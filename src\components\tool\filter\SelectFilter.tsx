"use client";

import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

type SelectOption = {
  label: string;
  value: string;
};

type SelectFilterProps = {
  value: string;
  chevronColor: string;
  placeholder?: string;
  list: SelectOption[];
  onSelect: (val: string) => void;
};

const SelectFilter: React.FC<SelectFilterProps> = ({
  value,
  list,
  onSelect,
  placeholder,
  chevronColor,
}) => {
  return (
    <Select value={value} onValueChange={onSelect}>
      <SelectTrigger
        className="w-32 bg-white rounded-full text-[#14141FCC]"
        chevronClassName={`text-[${chevronColor}]`}
      >
        <SelectValue placeholder={placeholder} className="text-[#14141FCC]" />
      </SelectTrigger>
      <SelectContent>
        {list.map((item) => (
          <SelectItem key={item.value} value={item.value}>
            {item.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

export default SelectFilter;
