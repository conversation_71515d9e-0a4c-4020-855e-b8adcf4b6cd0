"use client";

import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { <PERSON>ton } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { StarIcon } from "lucide-react";

const LeadGeneratorTool = () => {
  const [activeTab, setActiveTab] = useState("new-leads");
  const [selectedLeads, setSelectedLeads] = useState<string[]>([]);

  // Mock data for demonstration
  const leadSummaryData = {
    totalLeadCollected: 3500,
    totalLeadContacted: 109,
    totalLeadConversion: 7,
  };

  const creditUsageData = {
    availableCredits: 3500,
    dailyLimit: 25,
    monthlyLimit: 2500,
  };

  const mockLeads = [
    {
      id: "1",
      domain: "multiple-problems.com",
      contact: "Alyson",
      prospected: "Yes 10/10",
      teams: "Teams 1, Teams 4",
      score: 5,
      status: "Todo",
    },
    {
      id: "2",
      domain: "shop-family.com",
      contact: "Alyson",
      prospected: "Yes 10/10",
      teams: "Teams 1, Teams 4",
      score: 4,
      status: "Todo",
    },
    {
      id: "3",
      domain: "store.com",
      contact: "Alyson",
      prospected: "Yes 10/10",
      teams: "Teams 1, Teams 4",
      score: 4,
      status: "Todo",
    },
    {
      id: "4",
      domain: "tech-solutions.com",
      contact: "Michael",
      prospected: "Yes 8/10",
      teams: "Teams 2, Teams 3",
      score: 3,
      status: "Todo",
    },
    {
      id: "5",
      domain: "business-hub.net",
      contact: "Sarah",
      prospected: "No 0/10",
      teams: "Teams 1",
      score: 5,
      status: "Todo",
    },
  ];

  const handleLeadSelection = (leadId: string, checked: boolean) => {
    if (checked) {
      setSelectedLeads([...selectedLeads, leadId]);
    } else {
      setSelectedLeads(selectedLeads.filter((id) => id !== leadId));
    }
  };

  const renderStars = (count: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <StarIcon
        key={i}
        className={`w-4 h-4 ${
          i < count ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
        }`}
      />
    ));
  };

  return (
    <div className="flex-1 h-full">
      <Card className="h-full glassColor p-6 space-y-6">
        {/* Lead Summary and Credit Usage Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Lead Summary Card */}
          <Card className="p-6 bg-white/70 border-white/30">
            <div className="text-center">
              <h3 className="text-sm font-medium text-gray-600 mb-4">
                Lead summary
              </h3>
              <div className="space-y-4">
                <div>
                  <p className="text-xs text-gray-500 mb-1">
                    Total Lead Collected
                  </p>
                  <p className="text-3xl font-bold text-gray-900">
                    {leadSummaryData.totalLeadCollected.toLocaleString()}
                  </p>
                </div>
                <div className="grid grid-cols-2 gap-6 text-center">
                  <div>
                    <p className="text-xs text-gray-500 mb-1">
                      Total Lead Contacted
                    </p>
                    <p className="text-2xl font-bold text-gray-900">
                      {leadSummaryData.totalLeadContacted}
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500 mb-1">
                      Total Lead Conversion
                    </p>
                    <p className="text-2xl font-bold text-gray-900">
                      {leadSummaryData.totalLeadConversion}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </Card>

          {/* Credit & Usage Card */}
          <Card className="p-6 bg-white/70 border-white/30">
            <div className="text-center">
              <h3 className="text-sm font-medium text-gray-600 mb-4">
                Credit & Usage
              </h3>
              <div className="space-y-4">
                <div>
                  <p className="text-xs text-gray-500 mb-1">
                    Available Credits
                  </p>
                  <p className="text-3xl font-bold text-blue-600">
                    {creditUsageData.availableCredits.toLocaleString()}
                  </p>
                </div>
                <div className="grid grid-cols-2 gap-6 text-center">
                  <div>
                    <p className="text-xs text-gray-500 mb-1">Daily Limit</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {creditUsageData.dailyLimit}/
                      <span className="text-sm">day</span>
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500 mb-1">Monthly Limit</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {creditUsageData.monthlyLimit}/
                      <span className="text-sm">month</span>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* Filter Section */}
        <Card className="p-6 bg-white/50 border-white/30">
          <div className="space-y-6">
            <div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Filter</h3>
              <p className="text-sm text-gray-600">
                Filter leads can apply only one filter at a keyword or a
                Platform.
              </p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
              <div className="space-y-2">
                <Label
                  htmlFor="contact"
                  className="text-sm font-medium text-gray-700"
                >
                  Contact
                </Label>
                <Select>
                  <SelectTrigger className="bg-white/80 border-gray-200 h-10">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="contacted">Contacted</SelectItem>
                    <SelectItem value="not-contacted">Not Contacted</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="prospected"
                  className="text-sm font-medium text-gray-700"
                >
                  Prospected
                </Label>
                <Select>
                  <SelectTrigger className="bg-white/80 border-gray-200 h-10">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="yes">Yes</SelectItem>
                    <SelectItem value="no">No</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="theme"
                  className="text-sm font-medium text-gray-700"
                >
                  Theme
                </Label>
                <Select>
                  <SelectTrigger className="bg-white/80 border-gray-200 h-10">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="business">Business</SelectItem>
                    <SelectItem value="ecommerce">E-commerce</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="socials"
                  className="text-sm font-medium text-gray-700"
                >
                  Socials
                </Label>
                <Select>
                  <SelectTrigger className="bg-white/80 border-gray-200 h-10">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="facebook">Facebook</SelectItem>
                    <SelectItem value="instagram">Instagram</SelectItem>
                    <SelectItem value="twitter">Twitter</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="status"
                  className="text-sm font-medium text-gray-700"
                >
                  Status
                </Label>
                <Select>
                  <SelectTrigger className="bg-white/80 border-gray-200 h-10">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="todo">Todo</SelectItem>
                    <SelectItem value="in-progress">In Progress</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="eot"
                  className="text-sm font-medium text-gray-700"
                >
                  EOT
                </Label>
                <Select>
                  <SelectTrigger className="bg-white/80 border-gray-200 h-10">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="option1">Option 1</SelectItem>
                    <SelectItem value="option2">Option 2</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-gray-700">
                  Selected Filters:
                </span>
                <Badge
                  variant="outline"
                  className="bg-red-50 text-red-700 border-red-200"
                >
                  Filter limit reached. Upgrade plan for more.
                </Badge>
              </div>
              <Button className="primaryButton text-white px-6 py-2 rounded-lg font-medium">
                Search Copyright
              </Button>
            </div>
          </div>
        </Card>

        {/* Tabs and Table Section */}
        <div className="space-y-4">
          {/* Tab Navigation */}
          <div className="flex space-x-1 bg-white/30 p-1 rounded-lg w-fit">
            <button
              onClick={() => setActiveTab("new-leads")}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === "new-leads"
                  ? "bg-white text-gray-900 shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              New leads
            </button>
            <button
              onClick={() => setActiveTab("purchased-leads")}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === "purchased-leads"
                  ? "bg-white text-gray-900 shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              My Purchased leads
            </button>
          </div>

          {/* Results Summary */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 bg-white/30 p-4 rounded-lg">
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
              <span className="text-sm font-medium text-gray-700">
                Filtered Results:{" "}
                <span className="font-bold text-gray-900">4000</span>
              </span>
              <span className="text-sm font-medium text-gray-700">
                New Leads:{" "}
                <span className="font-bold text-gray-900">2,584</span>
              </span>
            </div>
            <div className="flex items-center gap-3">
              <span className="text-sm font-medium text-gray-700">
                Get Lead
              </span>
              <Button className="primaryButton text-white px-6 py-2 rounded-lg font-medium">
                Get Lead (1 Credits)
              </Button>
            </div>
          </div>

          {/* Table */}
          <Card className="bg-white/80 border-white/30 overflow-hidden">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="border-gray-200 bg-gray-50/50">
                    <TableHead className="w-12 py-4">
                      <Checkbox />
                    </TableHead>
                    <TableHead className="font-semibold text-gray-900 py-4">
                      Domain
                    </TableHead>
                    <TableHead className="font-semibold text-gray-900 py-4">
                      Contact
                    </TableHead>
                    <TableHead className="font-semibold text-gray-900 py-4">
                      Prospected
                    </TableHead>
                    <TableHead className="font-semibold text-gray-900 py-4">
                      Teams
                    </TableHead>
                    <TableHead className="font-semibold text-gray-900 py-4">
                      Score
                    </TableHead>
                    <TableHead className="font-semibold text-gray-900 py-4">
                      Status
                    </TableHead>
                    <TableHead className="w-40 py-4"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {mockLeads.map((lead) => (
                    <TableRow
                      key={lead.id}
                      className="border-gray-100 hover:bg-gray-50/30"
                    >
                      <TableCell className="py-4">
                        <Checkbox
                          checked={selectedLeads.includes(lead.id)}
                          onCheckedChange={(checked) =>
                            handleLeadSelection(lead.id, checked as boolean)
                          }
                        />
                      </TableCell>
                      <TableCell className="font-medium text-gray-900 py-4">
                        {lead.domain}
                      </TableCell>
                      <TableCell className="text-blue-600 py-4 font-medium">
                        {lead.contact}
                      </TableCell>
                      <TableCell className="text-gray-700 py-4">
                        {lead.prospected}
                      </TableCell>
                      <TableCell className="text-gray-700 py-4">
                        {lead.teams}
                      </TableCell>
                      <TableCell className="py-4">
                        <div className="flex items-center gap-1">
                          {renderStars(lead.score)}
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        <Badge
                          variant="outline"
                          className="bg-yellow-100 text-yellow-800 border-yellow-300 font-medium"
                        >
                          {lead.status}
                        </Badge>
                      </TableCell>
                      <TableCell className="py-4">
                        <Button
                          size="sm"
                          className="primaryButton text-white px-4 py-2 text-xs rounded-lg font-medium"
                        >
                          Add Leads to Cart
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </Card>
        </div>
      </Card>
    </div>
  );
};

export default LeadGeneratorTool;
