"use client";

import { useUser } from "@/components/AppProvider";

const CreditUsage = () => {
  const userData = useUser();

  const leadsSummaryData = [
    {
      id: 2,
      name: "Daily Limit",
      used: 0,
      limit: userData?.plan?.daily_cap ?? 0,
    },
    {
      id: 3,
      name: "Monthly Limit",
      used: 0,
      limit: userData?.plan?.monthly_cap ?? 0,
    },
  ];

  return (
    <div className="w-full flex flex-col md:flex-row gap-2">
      <MainCard />
      {leadsSummaryData.map((m, i) => (
        <StatsCard data={m} key={i} />
      ))}
    </div>
  );
};

type StatsCardProps = React.FC<{
  data: {
    id: number;
    name: string;
    used: number;
    limit: number;
  };
}>;
const StatsCard: StatsCardProps = ({ data }) => {
  return (
    <div className="rounded-sm glassColor p-3 w-full  h-[110px]">
      <p className="text-[0.8rem] text-center font-bold text-[#14141FCC]">
        {data.name}
      </p>
      <div className="flex flex-row items-baseline justify-center">
        <h2 className="m-0 font-bold text-3xl">{data.used}/</h2>
        <span className="h-full font-bold text-[0.85rem]">{data.limit}</span>
      </div>
      <Bar percentage={(data.used / data.limit) * 100} />
    </div>
  );
};

type BarProps = React.FC<{
  percentage: number;
}>;
const Bar: BarProps = ({ percentage }) => {
  return (
    <div className="w-full flex flex-row items-center gap-2">
      <div className="w-full bg-white h-2 rounded-full">
        <div
          className="h-full rounded-full"
          style={{
            width: `${percentage}%`,
            background: "#028482",
          }}
        />
      </div>
      <p className="mb-0 font-bold text-[0.75rem] text-[#028482]">
        {percentage}%
      </p>
    </div>
  );
};

const MainCard = () => {
  return (
    <div className="rounded-sm flex flex-col items-center justify-center bg-[#0060D0CC] text-[#fff] p-3 h-[110px] w-full">
      <p className="text-[0.8rem] text-center font-bold ">Available Credits</p>
      <h2 className="text-center text-[1.85rem] font-extrabold">3.5k</h2>
    </div>
  );
};

export default CreditUsage;
