"use client";

import { Skeleton } from "@/components/ui/skeleton";
import React, { useEffect, useState } from "react";
import { toast } from "sonner";

const LeadSummary = () => {
  const [leadsValues, setLeadsValues] = useState({
    total_lead_contacted: 0,
    total_lead_conversion: 0,
    total_leads_collected: 0,
  });
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const handleError = () => {
      setIsLoading(false);
      toast.error("Something went wrong while fetching credit usage");
    };

    const fetchData = async () => {
      try {
        setIsLoading(true);
        const response = await fetch("/api/analytics/lead", {
          method: "GET",
        });
        if (!response.ok) {
          handleError();
          return;
        }
        const responseData = await response.json();
        if (responseData.success) {
          setLeadsValues(responseData.data);
        } else {
          handleError();
        }
        setIsLoading(false);
      } catch (error) {
        console.error("Something went wrong in fetchData due to ", error);
        handleError();
      }
    };
    fetchData();
  }, []);

  const leadsSummaryData = [
    {
      id: 1,
      name: "Total Lead Collected",
      value: leadsValues.total_leads_collected,
    },
    {
      id: 2,
      name: "Total Lead Contacted",
      value: leadsValues.total_lead_contacted,
    },
    {
      id: 3,
      name: "Total Lead Conversion",
      value: leadsValues.total_lead_conversion,
    },
  ];

  return (
    <div className="w-full flex flex-col md:flex-row gap-2">
      {leadsSummaryData.map((m, i) => (
        <div
          key={i}
          className="rounded-sm h-[110px] flex flex-col items-center justify-center glassColor p-3"
        >
          <p className="text-[0.8rem] text-center font-bold text-[#14141FCC]">
            {m.name}
          </p>
          {isLoading ? (
            <Skeleton className="h-8 w-[100px] rounded-full" />
          ) : (
            <h2 className="text-center text-[2rem] font-extrabold">
              {m.value}
            </h2>
          )}
        </div>
      ))}
    </div>
  );
};

export default LeadSummary;
