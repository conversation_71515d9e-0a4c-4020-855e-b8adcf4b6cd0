import Filters from "@/components/tool/filter";
import CreditUsage from "@/components/tool/header/CreditUsage";
import HeaderCardsContainer from "@/components/tool/header/headerCardsContainer";
import LeadSummary from "@/components/tool/header/LeadSummary";
import Leads from "@/components/tool/leads";
import { Card } from "@/components/ui/card";
import React from "react";

const LeadGeneratorTool = async () => {
  return (
    <div className="flex-1 h-full">
      <Card className="h-full glassColor relative p-4">
        <div className="flex flex-col lg:flex-row gap-2">
          <HeaderCardsContainer title="Lead Summary">
            <LeadSummary />
          </HeaderCardsContainer>
          <HeaderCardsContainer title="Credit & Usage">
            <CreditUsage />
          </HeaderCardsContainer>
        </div>
        <Filters />
        <Card className="h-full glassColor relative p-4">
          <Leads />
        </Card>
      </Card>
    </div>
  );
};

export default LeadGeneratorTool;
