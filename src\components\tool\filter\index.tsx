"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Info } from "lucide-react";
import React from "react";
import SelectFilter from "./SelectFilter";

const Filters = () => {
  return (
    <div className="glassColor p-4 rounded-sm">
      <div className="pb-3">
        <h2 className="text-lg font-semibold text-gray-900">Filter</h2>
        <div className="text-sm text-[#888888] flex flex-row items-center gap-1">
          <Info className="h-3 w-3" />
          Free users can apply only one filter i.e 1 Keyword or 1 Platform.
        </div>
      </div>
      <div className="flex flex-wrap gap-3 mb-4">
        <SelectFilter
          chevronColor="#0060D0"
          value="contact"
          placeholder="Select contact"
          list={[
            { label: "Contact", value: "contact" },
            { label: "Support", value: "support" },
            { label: "Feedback", value: "feedback" },
          ]}
          onSelect={(val) => console.log("Selected:", val)}
        />
        <SelectFilter
          chevronColor="#44D5E5"
          value="Prospected"
          placeholder="Select Prospected"
          list={[
            { label: "Prospected", value: "Prospected" },
            { label: "Support", value: "support" },
            { label: "Feedback", value: "feedback" },
          ]}
          onSelect={(val) => console.log("Selected:", val)}
        />
        <SelectFilter
          chevronColor="#F55767"
          value="Theme"
          placeholder="Select Theme"
          list={[
            { label: "Theme", value: "Theme" },
            { label: "Support", value: "support" },
            { label: "Feedback", value: "feedback" },
          ]}
          onSelect={(val) => console.log("Selected:", val)}
        />
        <SelectFilter
          chevronColor="#FFEA03"
          value="Socials"
          placeholder="Select Socials"
          list={[
            { label: "Socials", value: "Socials" },
            { label: "Support", value: "support" },
            { label: "Feedback", value: "feedback" },
          ]}
          onSelect={(val) => console.log("Selected:", val)}
        />
        <SelectFilter
          chevronColor="#171717"
          value="Status"
          placeholder="Select Status"
          list={[
            { label: "Status", value: "Status" },
            { label: "Support", value: "support" },
            { label: "Feedback", value: "feedback" },
          ]}
          onSelect={(val) => console.log("Selected:", val)}
        />
        <SelectFilter
          value="ECT"
          chevronColor="#0060D0"
          placeholder="Select ECT"
          list={[
            { label: "ECT", value: "ECT" },
            { label: "Support", value: "support" },
            { label: "Feedback", value: "feedback" },
          ]}
          onSelect={(val) => console.log("Selected:", val)}
        />
      </div>

      <div className="flex items-center gap-2 text-[#5C5C5C] text-[0.75rem] ">
        Selected Filters
        <FiltersLimitReached />
        <span className="text-[#028482] cursor-pointer text-[0.75rem] font-bold">
          Upgrade plan
        </span>
        for more.
      </div>

      <Badge className="bg-[#006DFF] rounded-full px-4">Email</Badge>

      <div className="flex w-full items-center justify-end">
        <Button className="primaryButton text-white px-6 py-2 rounded-lg font-medium">
          Search completed
        </Button>
      </div>
    </div>
  );
};

const FiltersLimitReached = () => {
  return (
    <span className="text-[0.75rem] text-red-500 flex flex-row gap-1 items-center justify-center">
      <Info className="h-3 w-3 text-red-500" />
      Filter limit reached.
    </span>
  );
};

export default Filters;
